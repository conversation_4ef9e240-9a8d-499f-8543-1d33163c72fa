import playwright
from playwright.sync_api import sync_playwright
import time
import os

with sync_playwright() as p:
    # Try different profile directories
    profile_options = [
        r"C:\Users\<USER>\AppData\Local\Google\Chrome\User Data\Profile 1",
        r"C:\Users\<USER>\AppData\Local\Google\Chrome\User Data\Profile 2",
        r"C:\Users\<USER>\AppData\Local\Google\Chrome\User Data\Default"
    ]

    print("Available profiles:")
    for i, profile in enumerate(profile_options):
        exists = "✓" if os.path.exists(profile) else "✗"
        print(f"{i+1}. {profile} {exists}")

    success = False

    for user_data_dir in profile_options:
        if not os.path.exists(user_data_dir):
            continue

        print(f"\nTrying profile: {user_data_dir}")

        try:
            # Use launch_persistent_context instead of launch when using user data directory
            context = p.chromium.launch_persistent_context(
                user_data_dir=user_data_dir,
                headless=False,
                # Minimal args to avoid conflicts
                args=[
                    "--no-first-run",
                    "--disable-default-apps",
                    "--disable-extensions-except=*",
                    "--disable-background-timer-throttling"
                ]
            )

            page = context.new_page()
            print(f"✓ Browser launched successfully with profile: {user_data_dir}")
            page.goto("https://www.youtube.com")
            print("Navigated to YouTube")
            print("Check if you're logged in and can see your bookmarks!")
            print("Browser will stay open for 30 seconds...")
            time.sleep(30)
            context.close()
            print("Browser closed")
            success = True
            break

        except Exception as e:
            print(f"✗ Failed with profile {user_data_dir}: {e}")
            continue

    if not success:
        print("\nAll profiles failed. Let's try without user data (fresh browser):")
        try:
            browser = p.chromium.launch(headless=False)
            context = browser.new_context()
            page = context.new_page()
            page.goto("https://www.youtube.com")
            print("Fresh browser opened - you'll need to log in manually")
            time.sleep(30)
            browser.close()
        except Exception as e:
            print(f"Even fresh browser failed: {e}")